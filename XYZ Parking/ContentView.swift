//
//  ContentView.swift
//  XYZ Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import SwiftUI
import MapKit

// MARK: - Bottom Sheet Content Type
enum BottomSheetContentType {
    case list
    case detail
}

struct ContentView: View {
    @StateObject private var viewModel = ContentViewModel()
    @State private var showingLocationAlert = false
    @State private var showingBottomSheet = true // Controls native sheet presentation
    @State private var bottomSheetDetent: PresentationDetent = .height(90) // Control bottom sheet expansion
    @State private var centerOnParking: ParkingLocation? = nil // Track parking to center on map
    @State private var bottomSheetContentType: BottomSheetContentType = .list // Control what content to show
    @State private var selectedParkingLocation: ParkingLocation? = nil // Selected parking for detail view

    var body: some View {
        NavigationStack {
            ZStack {
                // Main Map View with custom user location
                MapViewRepresentable(
                    locationManager: viewModel.locationManager,
                    parkingLocations: viewModel.dataService.parkingLocations,
                    centerOnParking: $centerOnParking,
                    onParkingLocationSelected: { location in
                        // Set selected parking and switch to detail view
                        selectedParkingLocation = location
                        bottomSheetContentType = .detail
                        // Center map on selected parking location
                        centerOnParking = location
                        // Ensure bottom sheet is open and expanded
                        if !showingBottomSheet {
                            showingBottomSheet = true
                        }
                        // Expand bottom sheet to show details
                        bottomSheetDetent = .medium
                    }
                )
                .ignoresSafeArea()
                    .onAppear {
                        // LocationManager will automatically get initial location once in init
                        // Trigger initial data fetch once ready (or fallback)
                        print("📍 MapView appeared - location will be obtained once at startup")
                    }

                // Top Navigation Bar
                VStack {
                    HStack {
                        Text("XYZ Melbourne Parking")
                            .font(.title2)
                            .fontWeight(.bold)
                            .padding()
                            .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12, style: .continuous))

                        Spacer()

                        // Location Button
                        Button(action: {
                            viewModel.locationManager.recenterMapToUserLocation()
                        }) {
                            Image(systemName: "location.fill")
                                .font(.title2)
                                .foregroundStyle(.blue)
                        }
                        .padding(12)
                        .background(.regularMaterial)
                        .clipShape(Circle())
                        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 8)

                    Spacer()
                }

                // Bottom Sheet is now presented using .sheet() modifier
            }
        }
        .alert("Location Access Required", isPresented: $showingLocationAlert) {
            Button("Settings") {
                if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(settingsUrl)
                }
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("Please enable location access in Settings to find parking near you.")
        }
        .onAppear {
            viewModel.onAppear()
        }
        .onChange(of: viewModel.locationManager.authorizationStatus) { _, status in
            if status == .denied || status == .restricted {
                showingLocationAlert = true
            }
        }
        .onChange(of: viewModel.dataService.parkingLocations) { _, newLocations in
            print("🅿️ Parking locations updated: \(newLocations.count) spots available")
            if newLocations.isEmpty {
                print("⚠️ No parking locations loaded - check API connection")
            } else {
                print("✅ First few locations:")
                for (index, location) in newLocations.prefix(3).enumerated() {
                    print("  \(index+1). \(location.name) at (\(location.latitude), \(location.longitude)) - \(location.statusText)")
                }
            }
        }
        .sheet(isPresented: $showingBottomSheet) {
            ParkingBottomSheetView(
                searchText: $viewModel.searchText,
                parkingLocations: viewModel.filteredParkingLocations,
                userLocation: viewModel.locationManager.location,
                speechRecognizer: viewModel.speechRecognizer,
                contentType: $bottomSheetContentType,
                selectedParkingLocation: $selectedParkingLocation,
                isLoading: viewModel.dataService.isLoading,
                errorMessage: viewModel.dataService.errorMessage,
                onParkingLocationSelected: { location in
                    // Set selected parking and switch to detail view
                    selectedParkingLocation = location
                    bottomSheetContentType = .detail
                    bottomSheetDetent = .medium
                },
                onBackToList: {
                    // Return to list view
                    bottomSheetContentType = .list
                    selectedParkingLocation = nil
                    bottomSheetDetent = .medium
                },
                onRetry: {
                    viewModel.onRetry()
                }
            )
            .presentationDetents([.height(90), .medium, .large], selection: $bottomSheetDetent)
            .presentationDragIndicator(.visible)
            .presentationContentInteraction(.scrolls)
            .presentationBackgroundInteraction(.enabled)
            .presentationBackground(.ultraThinMaterial)
            .interactiveDismissDisabled(true)
        }
    }
}

#Preview {
    ContentView()
}

// MARK: - Parking Annotation
class ParkingAnnotation: NSObject, MKAnnotation {
    dynamic var coordinate: CLLocationCoordinate2D
    var title: String?
    var subtitle: String?
    let parkingLocation: ParkingLocation
    
    init(parkingLocation: ParkingLocation) {
        self.parkingLocation = parkingLocation
        self.coordinate = CLLocationCoordinate2D(latitude: parkingLocation.latitude, longitude: parkingLocation.longitude)
        self.title = parkingLocation.name
        self.subtitle = "\(parkingLocation.statusText) • \(parkingLocation.address)"
        super.init()
    }
}

// MARK: - MapView UIViewRepresentable
struct MapViewRepresentable: UIViewRepresentable {
    @ObservedObject var locationManager: LocationManager
    let parkingLocations: [ParkingLocation]
    @Binding var centerOnParking: ParkingLocation?
    let onParkingLocationSelected: (ParkingLocation) -> Void

    func makeUIView(context: Context) -> MKMapView {
        let mapView = MKMapView()
        mapView.delegate = context.coordinator
        mapView.showsUserLocation = false // We'll handle this ourselves
        mapView.userTrackingMode = .none
        mapView.showsCompass = true
        mapView.showsScale = true

        // Register custom annotation view
        mapView.register(UserLocationAnnotationView.self,
                        forAnnotationViewWithReuseIdentifier: UserLocationAnnotationView.identifier)

        // Initial parking annotations will be added in updateUIView
        return mapView
    }

    func updateUIView(_ mapView: MKMapView, context: Context) {
        // Handle centering on selected parking location
        if let parkingToCenter = centerOnParking {
            let latitudeOffset = -0.003

            let region = MKCoordinateRegion(
                center: CLLocationCoordinate2D(
                    latitude: parkingToCenter.latitude + latitudeOffset,
                    longitude: parkingToCenter.longitude
                ),
                latitudinalMeters: 1000, // 1km zoom level
                longitudinalMeters: 1000
            )
            mapView.setRegion(region, animated: true)

            // Reset the centering trigger
            DispatchQueue.main.async {
                centerOnParking = nil
            }
            return // Skip other region updates when centering on parking
        }

        // Update map region only when explicitly requested (initial location or manual centering)
        if locationManager.shouldCenterOnRegionUpdate {
            let currentCenter = mapView.region.center
            let newCenter = locationManager.region.center
            let threshold = 0.001 // Only update if difference is significant

            if abs(currentCenter.latitude - newCenter.latitude) > threshold ||
               abs(currentCenter.longitude - newCenter.longitude) > threshold {
                mapView.setRegion(locationManager.region, animated: true)
            }

            // Reset the flag after updating
            DispatchQueue.main.async {
                locationManager.shouldCenterOnRegionUpdate = false
            }
        }

        // Update parking annotations when data changes
        updateParkingAnnotations(mapView: mapView)

        // Update user location annotation (only user location, not parking annotations)
        if let userAnnotation = locationManager.userLocationAnnotation {
            if !mapView.annotations.contains(where: { $0 === userAnnotation }) {
                mapView.addAnnotation(userAnnotation)
            }

            // Update heading for existing annotation view
            if let annotationView = mapView.view(for: userAnnotation) as? UserLocationAnnotationView {
                annotationView.updateHeading(locationManager.userHeading)
            }
        }
    }

    private func updateParkingAnnotations(mapView: MKMapView) {
        // Get current parking annotations on the map
        let currentParkingAnnotations = mapView.annotations.compactMap { $0 as? ParkingAnnotation }
        
        // Get the IDs of current annotations
        let currentAnnotationIds = Set(currentParkingAnnotations.map { $0.parkingLocation.id })
        
        // Get the IDs of new parking locations
        let newLocationIds = Set(parkingLocations.map { $0.id })
        
        // Remove annotations that are no longer in the data
        let annotationsToRemove = currentParkingAnnotations.filter { annotation in
            !newLocationIds.contains(annotation.parkingLocation.id)
        }
        mapView.removeAnnotations(annotationsToRemove)
        
        // Add new annotations for locations not already on the map
        let locationsToAdd = parkingLocations.filter { location in
            !currentAnnotationIds.contains(location.id)
        }
        
        for location in locationsToAdd {
            let annotation = ParkingAnnotation(parkingLocation: location)
            mapView.addAnnotation(annotation)
        }
        
        print("📍 Map annotations updated: removed \(annotationsToRemove.count), added \(locationsToAdd.count), total: \(parkingLocations.count)")
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, MKMapViewDelegate {
        var parent: MapViewRepresentable

        init(_ parent: MapViewRepresentable) {
            self.parent = parent
        }

        func mapView(_ mapView: MKMapView, viewFor annotation: MKAnnotation) -> MKAnnotationView? {
            if annotation is UserLocationAnnotation {
                let annotationView = mapView.dequeueReusableAnnotationView(
                    withIdentifier: UserLocationAnnotationView.identifier,
                    for: annotation
                ) as! UserLocationAnnotationView

                // Update heading immediately
                annotationView.updateHeading(parent.locationManager.userHeading)

                return annotationView
            } else if let cluster = annotation as? MKClusterAnnotation {
                // Clustered parking annotations
                let identifier = "ParkingCluster"
                var clusterView = mapView.dequeueReusableAnnotationView(withIdentifier: identifier) as? MKMarkerAnnotationView

                if clusterView == nil {
                    clusterView = MKMarkerAnnotationView(annotation: annotation, reuseIdentifier: identifier)
                    clusterView?.canShowCallout = false
                } else {
                    clusterView?.annotation = annotation
                }

                // Customize cluster appearance
                clusterView?.markerTintColor = .systemBlue
                clusterView?.glyphText = "\(cluster.memberAnnotations.count)"
                clusterView?.displayPriority = .defaultHigh

                return clusterView
            } else if let parkingAnnotation = annotation as? ParkingAnnotation {
                // Handle parking annotations
                let identifier = "ParkingAnnotation"
                var annotationView = mapView.dequeueReusableAnnotationView(withIdentifier: identifier)

                if annotationView == nil {
                    annotationView = MKMarkerAnnotationView(annotation: annotation, reuseIdentifier: identifier)
                    annotationView?.canShowCallout = true
                    annotationView?.calloutOffset = CGPoint(x: -5, y: 5)
                } else {
                    annotationView?.annotation = annotation
                }

                // Customize the parking marker based on availability
                if let markerView = annotationView as? MKMarkerAnnotationView {
                    let parkingLocation = parkingAnnotation.parkingLocation
                    
                    // Set color based on occupancy status
                    markerView.markerTintColor = parkingLocation.isOccupied ? .systemRed : .systemGreen
                    markerView.glyphImage = UIImage(systemName: "car.fill")
                    // Enable clustering to reduce number of visible annotations
                    markerView.clusteringIdentifier = "parking"
                    markerView.displayPriority = .defaultLow
                    
                    // Make the annotation more prominent
                    // Disable animation for large batch adds to reduce CPU cost
                    markerView.animatesWhenAdded = false
                    markerView.isDraggable = false
                    markerView.canShowCallout = true
                }

                return annotationView
            }
            return nil
        }

        func mapView(_ mapView: MKMapView, didAdd views: [MKAnnotationView]) {
            // Update heading for newly added user location annotation
            for view in views {
                if let userLocationView = view as? UserLocationAnnotationView {
                    userLocationView.updateHeading(parent.locationManager.userHeading)
                }
            }
        }
        
        func mapView(_ mapView: MKMapView, didSelect view: MKAnnotationView) {
            // Handle parking location selection
            if let parkingAnnotation = view.annotation as? ParkingAnnotation {
                parent.onParkingLocationSelected(parkingAnnotation.parkingLocation)
            }
        }
    }
}



// MARK: - Parking Bottom Sheet View
struct ParkingBottomSheetView: View {
    @Binding var searchText: String
    let parkingLocations: [ParkingLocation]
    let userLocation: CLLocation?
    @ObservedObject var speechRecognizer: SpeechRecognizer
    @Binding var contentType: BottomSheetContentType
    @Binding var selectedParkingLocation: ParkingLocation?
    let isLoading: Bool
    let errorMessage: String?
    let onParkingLocationSelected: (ParkingLocation) -> Void
    let onBackToList: () -> Void
    let onRetry: () -> Void

    var body: some View {
        VStack(spacing: 0) {
            // Top bar with search or back button
            if contentType == .list {
                // Search bar (visible in list mode)
                HStack(spacing: 12) {
                    HStack(spacing: 8) {
                        Image(systemName: "magnifyingglass")
                            .foregroundStyle(.secondary)
                            .font(.system(size: 16))

                        TextField("Search", text: $searchText)
                            .textFieldStyle(.plain)
                            .font(.system(size: 16))

                        if !searchText.isEmpty {
                            Button(action: {
                                searchText = ""
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundStyle(.secondary)
                                    .font(.system(size: 16))
                            }
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 25)
                            .fill(Color(.systemGray6))
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 25)
                            .stroke(Color(.systemGray4), lineWidth: 1)
                    )

                    // Voice search button
                    Button(action: {
                        if speechRecognizer.isRecording {
                            speechRecognizer.stopRecording()
                        } else {
                            speechRecognizer.startRecording()
                        }
                    }) {
                        Image(systemName: "mic.fill")
                            .font(.system(size: 20))
                            .foregroundStyle(.white)
                            .frame(width: 50, height: 50)
                            .background(Circle().fill(speechRecognizer.isRecording ? .red : .blue))
                    }
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            } else {
                // Back button bar (visible in detail mode)
                HStack {
                    Button(action: onBackToList) {
                        HStack(spacing: 8) {
                            Image(systemName: "chevron.left")
                                .font(.system(size: 16, weight: .medium))
                            Text("Back to List")
                                .font(.system(size: 16, weight: .medium))
                        }
                        .foregroundStyle(.blue)
                    }
                    
                    Spacer()
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }

            // Scrollable content
            ScrollViewReader { proxy in
                ScrollView {
                    VStack {
                        if contentType == .list {
                            ParkingListContentView(
                                parkingLocations: parkingLocations,
                                searchText: searchText,
                                userLocation: userLocation,
                                isLoading: isLoading,
                                errorMessage: errorMessage,
                                onParkingLocationSelected: onParkingLocationSelected,
                                onRetry: onRetry
                            )
                            .padding(.top, 4)
                        } else if let selectedLocation = selectedParkingLocation {
                            ParkingDetailContentView(parkingLocation: selectedLocation)
                                .padding(.top, 4)
                        }
                    }
                    .id("top_scroll_view")
                }
                .onChange(of: contentType) {
                    withAnimation {
                        proxy.scrollTo("top_scroll_view", anchor: .top)
                    }
                }
                .onChange(of: selectedParkingLocation) {
                    if contentType == .detail {
                        withAnimation {
                            proxy.scrollTo("top_scroll_view", anchor: .top)
                        }
                    }
                }
            }
        }
        .background(
            UnevenRoundedRectangle(
                topLeadingRadius: 16,
                bottomLeadingRadius: 0,
                bottomTrailingRadius: 0,
                topTrailingRadius: 16
            )
            .fill(.ultraThinMaterial)
            .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: -5)
            .ignoresSafeArea(.all, edges: .bottom)
        )
    }
}

// MARK: - Parking List Content View (without NavigationStack)
struct ParkingListContentView: View {
    let parkingLocations: [ParkingLocation]
    let searchText: String
    let userLocation: CLLocation?
    let isLoading: Bool
    let errorMessage: String?
    let onParkingLocationSelected: (ParkingLocation) -> Void
    let onRetry: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            // Section title
            HStack {
                Text("Parking Nearby")
                    .font(.title3)
                    .fontWeight(.bold)
                    .foregroundStyle(.primary)
                Spacer()
            }
            .padding(.horizontal, 20)

            // Parking list
            LazyVStack(spacing: 16) {
                if isLoading {
                    VStack(spacing: 12) {
                        ProgressView()
                            .scaleEffect(1.2)
                        Text("Loading parking spots...")
                            .font(.headline)
                            .foregroundStyle(.secondary)
                    }
                    .padding(.top, 40)
                } else if let errorMessage = errorMessage {
                    VStack(spacing: 12) {
                        Image(systemName: "exclamationmark.triangle")
                            .font(.system(size: 40))
                            .foregroundStyle(.red)
                        Text("Error loading data")
                            .font(.headline)
                            .foregroundStyle(.secondary)
                        Text(errorMessage)
                            .font(.caption)
                            .foregroundStyle(.secondary)
                            .multilineTextAlignment(.center)

                        Button("Retry") {
                            onRetry()
                        }
                        .buttonStyle(.borderedProminent)
                    }
                    .padding(.top, 40)
                } else if parkingLocations.isEmpty {
                    VStack(spacing: 12) {
                        Image(systemName: "magnifyingglass")
                            .font(.system(size: 40))
                            .foregroundStyle(.secondary)
                        Text("No parking locations found")
                            .font(.headline)
                            .foregroundStyle(.secondary)
                        Text("Try adjusting your search terms")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                    }
                    .padding(.top, 40)
                } else {
                    ForEach(Array(parkingLocations.enumerated()), id: \.element.id) { index, location in
                        ParkingLocationCard(
                            parkingLocation: location,
                            userLocation: userLocation,
                            isTopCard: index == 0,
                            onTap: {
                                onParkingLocationSelected(location)
                            }
                        )
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 20)
        }
    }
}

// MARK: - Parking Detail Content View (adapted for bottomsheet)
struct ParkingDetailContentView: View {
    let parkingLocation: ParkingLocation
    
    var body: some View {
        VStack(alignment: .leading, spacing: 24) {
            // Street view map
            StreetViewMapView(
                coordinate: CLLocationCoordinate2D(
                    latitude: parkingLocation.latitude,
                    longitude: parkingLocation.longitude
                ),
                parkingName: parkingLocation.name
            )
            .frame(height: 200)
            .clipShape(RoundedRectangle(cornerRadius: 16))
            .padding(.horizontal, 20)
            
            // Details section
            VStack(alignment: .leading, spacing: 20) {
                // Title and location info
                VStack(alignment: .leading, spacing: 8) {
                    Text(parkingLocation.name)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundStyle(.primary)

                    if let onStreet = parkingLocation.onStreet {
                        HStack(spacing: 6) {
                            Image(systemName: "road.lanes")
                                .font(.system(size: 14))
                                .foregroundStyle(.blue)
                            Text("On \(onStreet.titleCased)")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundStyle(.primary)
                        }
                    }

                    HStack(spacing: 6) {
                        Image(systemName: "location.fill")
                            .font(.system(size: 14))
                            .foregroundStyle(.blue)
                        Text(parkingLocation.address.titleCased)
                            .font(.subheadline)
                            .foregroundStyle(.secondary)
                    }

                    if let zoneId = parkingLocation.zoneId {
                        HStack(spacing: 6) {
                            Image(systemName: "location.circle.fill")
                                .font(.system(size: 14))
                                .foregroundStyle(.blue)
                            Text("Zone \(zoneId)")
                                .font(.subheadline)
                                .foregroundStyle(.secondary)
                        }
                    }
                }

                // Status and restriction info
                VStack(spacing: 16) {
                    HStack(spacing: 24) {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Status")
                                .font(.caption)
                                .foregroundStyle(.secondary)
                            HStack(spacing: 6) {
                                Image(systemName: parkingLocation.isOccupied ? "car.fill" : "checkmark.circle.fill")
                                    .font(.system(size: 16))
                                    .foregroundStyle(parkingLocation.isOccupied ? .red : .green)
                                Text(parkingLocation.statusText)
                                    .font(.title3)
                                    .fontWeight(.bold)
                                    .foregroundStyle(parkingLocation.isOccupied ? .red : .green)
                            }
                        }

                        VStack(alignment: .leading, spacing: 4) {
                            Text("Spot Type")
                                .font(.caption)
                                .foregroundStyle(.secondary)
                            Text("Street Parking")
                                .font(.title3)
                                .fontWeight(.bold)
                                .foregroundStyle(.blue)
                        }
                    }

                    // Parking restrictions
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Parking Restrictions")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundStyle(.primary)

                        VStack(spacing: 8) {
                            if let restrictionDisplay = parkingLocation.restrictionDisplay,
                               let startTime = parkingLocation.restrictionStartTime,
                               let endTime = parkingLocation.restrictionFinishTime,
                               let day = parkingLocation.restrictionDay {

                                HStack {
                                    Image(systemName: "clock.fill")
                                        .foregroundStyle(.orange)
                                    Text("Time Limit:")
                                        .fontWeight(.medium)
                                    Spacer()
                                    Text(restrictionDisplay)
                                        .fontWeight(.bold)
                                        .foregroundStyle(.orange)
                                }

                                HStack {
                                    Image(systemName: "calendar")
                                        .foregroundStyle(.blue)
                                    Text("Days:")
                                        .fontWeight(.medium)
                                    Spacer()
                                    Text(parkingLocation.formatDay(day))
                                        .fontWeight(.bold)
                                        .foregroundStyle(.blue)
                                }

                                HStack {
                                    Image(systemName: "clock.arrow.circlepath")
                                        .foregroundStyle(.purple)
                                    Text("Hours:")
                                        .fontWeight(.medium)
                                    Spacer()
                                    Text("\(parkingLocation.formatTime(startTime)) - \(parkingLocation.formatTime(endTime))")
                                        .fontWeight(.bold)
                                        .foregroundStyle(.purple)
                                }

                                HStack {
                                    Image(systemName: "timer")
                                        .foregroundStyle(.green)
                                    Text("Max Stay:")
                                        .fontWeight(.medium)
                                    Spacer()
                                    Text(parkingLocation.maxStayText)
                                        .fontWeight(.bold)
                                        .foregroundStyle(.green)
                                }
                            } else {
                                HStack {
                                    Image(systemName: "checkmark.circle.fill")
                                        .foregroundStyle(.green)
                                    Text("No restrictions")
                                        .fontWeight(.medium)
                                        .foregroundStyle(.green)
                                    Spacer()
                                }
                            }
                        }
                        .font(.subheadline)
                    }
                    .padding(16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(.ultraThinMaterial)
                    )
                }

                // Action buttons
                Button(action: {
                    // Navigate action
                    if let url = URL(string: "maps://?daddr=\(parkingLocation.latitude),\(parkingLocation.longitude)") {
                        if UIApplication.shared.canOpenURL(url) {
                            UIApplication.shared.open(url)
                        }
                    }
                }) {
                    HStack {
                        Image(systemName: "location.north.fill")
                            .font(.system(size: 16, weight: .medium))
                        Text("Navigate")
                            .font(.system(size: 16, weight: .semibold))
                    }
                    .foregroundStyle(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(Color.blue)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                }
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.thinMaterial)
                    .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
            )
            .padding(.horizontal, 20)
        }
        .padding(.bottom, 20)
    }
}
