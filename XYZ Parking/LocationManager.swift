//
//  LocationManager.swift
//  XYZ Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import Foundation
import CoreLocation
import MapKit
import SwiftUI
import Combine

class LocationManager: NSObject, ObservableObject {
    private let locationManager = CLLocationManager()
    private var cancellables = Set<AnyCancellable>()
    private var hasObtainedInitialLocation = false // 标记是否已获取初始位置

    @Published var location: CLLocation?
    @Published var region = MKCoordinateRegion(
        center: CLLocationCoordinate2D(latitude: -37.8136, longitude: 144.9631), // Melbourne, Australia
        span: MKCoordinateSpan(latitudeDelta: 0.05, longitudeDelta: 0.05)
    )
    @Published var shouldCenterOnRegionUpdate = false // 控制是否应该自动居中
    @Published var authorizationStatus: CLAuthorizationStatus = .notDetermined
    @Published var userHeading: CLLocationDirection = 0.0
    @Published var userLocationAnnotation: UserLocationAnnotation?

    override init() {
        super.init()
        locationManager.delegate = self
        locationManager.desiredAccuracy = kCLLocationAccuracyBest

        // Enable heading updates for compass functionality
        if CLLocationManager.headingAvailable() {
            locationManager.headingFilter = 5.0 // Update every 5 degrees
        }

        // Get current authorization status
        authorizationStatus = locationManager.authorizationStatus

        // Request permission if not determined
        if authorizationStatus == .notDetermined {
            locationManager.requestWhenInUseAuthorization()
        } else if authorizationStatus == .authorizedWhenInUse || authorizationStatus == .authorizedAlways {
            // If already authorized, get initial location once
            getInitialLocationOnce()
        }
    }
    
    func requestLocation() {
        // 如果已经获取过初始位置，则不再请求
        guard !hasObtainedInitialLocation else {
            print("📍 Initial location already obtained, skipping location request")
            return
        }

        guard authorizationStatus == .authorizedWhenInUse || authorizationStatus == .authorizedAlways else {
            locationManager.requestWhenInUseAuthorization()
            return
        }

        getInitialLocationOnce()
    }

    // 新方法：只获取一次初始位置
    private func getInitialLocationOnce() {
        guard !hasObtainedInitialLocation else { return }

        // Use startUpdatingLocation instead of requestLocation for better reliability
        locationManager.startUpdatingLocation()

        // Start heading updates if available (只在获取位置时启动)
        if CLLocationManager.headingAvailable() {
            locationManager.startUpdatingHeading()
        }

        // Stop after getting first location to save battery
        DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
            if self.hasObtainedInitialLocation {
                self.locationManager.stopUpdatingLocation()
                print("📍 Stopped location updates after obtaining initial location")
            }
        }
    }
    
    public func recenterMapToUserLocation() {
        if let location = location {
            // Location is available, center immediately.
            self.shouldCenterOnRegionUpdate = true
            self.region = MKCoordinateRegion(
                center: location.coordinate,
                span: MKCoordinateSpan(latitudeDelta: 0.01, longitudeDelta: 0.01)
            )
        }
        // If location is not available, do nothing
    }

    // 保留这些方法以防其他地方需要，但不会自动调用
    func startLocationUpdates() {
        print("⚠️ startLocationUpdates called - but location is only obtained once at startup")
        // 不再自动启动位置更新，因为我们只在启动时获取一次位置
    }

    func stopLocationUpdates() {
        locationManager.stopUpdatingLocation()
        locationManager.stopUpdatingHeading()
        print("📍 Location updates stopped")
    }
}

extension LocationManager: CLLocationManagerDelegate {
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let location = locations.last else { return }

        print("📍 Location updated: \(location.coordinate.latitude), \(location.coordinate.longitude)")

        DispatchQueue.main.async {
            let isFirstUpdate = self.location == nil
            self.location = location

            if isFirstUpdate {
                self.hasObtainedInitialLocation = true
                self.shouldCenterOnRegionUpdate = true
                self.region = MKCoordinateRegion(
                    center: location.coordinate,
                    span: MKCoordinateSpan(latitudeDelta: 0.01, longitudeDelta: 0.01)
                )

                // 获取到初始位置后立即停止位置更新
                self.locationManager.stopUpdatingLocation()
                print("📍 Initial location obtained, stopped location updates")
            }

            // Update or create user location annotation
            self.updateUserLocationAnnotation(coordinate: location.coordinate)
        }
    }

    func locationManager(_ manager: CLLocationManager, didUpdateHeading newHeading: CLHeading) {
        guard newHeading.headingAccuracy >= 0 else { return }

        DispatchQueue.main.async {
            self.userHeading = newHeading.trueHeading >= 0 ? newHeading.trueHeading : newHeading.magneticHeading

            // Update annotation heading
            if let annotation = self.userLocationAnnotation {
                annotation.heading = self.userHeading
            }
        }
    }

    private func updateUserLocationAnnotation(coordinate: CLLocationCoordinate2D) {
        if let annotation = userLocationAnnotation {
            annotation.coordinate = coordinate
            annotation.heading = userHeading
        } else {
            let annotation = UserLocationAnnotation()
            annotation.coordinate = coordinate
            annotation.heading = userHeading
            userLocationAnnotation = annotation
        }
    }
    
    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        print("Location error: \(error.localizedDescription)")
    }
    
    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        DispatchQueue.main.async {
            self.authorizationStatus = status

            switch status {
            case .authorizedWhenInUse, .authorizedAlways:
                // 只在首次授权时获取一次位置
                if !self.hasObtainedInitialLocation {
                    self.getInitialLocationOnce()
                }
            case .denied, .restricted:
                // Keep Melbourne as default location
                print("📍 Location access denied or restricted")
                break
            case .notDetermined:
                manager.requestWhenInUseAuthorization()
            @unknown default:
                break
            }
        }
    }
}

// MARK: - Custom User Location Annotation
class UserLocationAnnotation: NSObject, MKAnnotation {
    @objc dynamic var coordinate: CLLocationCoordinate2D
    var heading: CLLocationDirection = 0.0 {
        didSet {
            // Notify observers that the annotation has changed
            willChangeValue(for: \.coordinate)
            didChangeValue(for: \.coordinate)
        }
    }

    override init() {
        self.coordinate = CLLocationCoordinate2D(latitude: 0, longitude: 0)
        super.init()
    }
}

// MARK: - Custom Annotation View
class UserLocationAnnotationView: MKAnnotationView {
    static let identifier = "UserLocationAnnotationView"

    private let arrowImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.tintColor = .systemBlue
        return imageView
    }()



    override init(annotation: MKAnnotation?, reuseIdentifier: String?) {
        super.init(annotation: annotation, reuseIdentifier: reuseIdentifier)
        setupView()
    }

    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setupView()
    }

    private func setupView() {
        frame = CGRect(x: 0, y: 0, width: 70, height: 70)
        centerOffset = CGPoint(x: 0, y: 0)

        // Add arrow image
        addSubview(arrowImageView)
        arrowImageView.frame = CGRect(x: 10, y: 10, width: 50, height: 50)

        // Create navigation arrow using SF Symbols with custom colors
        if let arrowImage = UIImage(systemName: "location.north.circle.fill") {
            // Configure the image with palette colors: blue arrow, white background
            let config = UIImage.SymbolConfiguration(paletteColors: [.systemBlue, .white])
            let coloredImage = arrowImage.withConfiguration(config)
            arrowImageView.image = coloredImage
        } else {
            // Fallback to a simple arrow shape
            arrowImageView.image = createArrowImage()
        }

        // Add shadow to the arrow for better visibility
        arrowImageView.layer.shadowColor = UIColor.black.cgColor
        arrowImageView.layer.shadowOffset = CGSize(width: 0, height: 1)
        arrowImageView.layer.shadowOpacity = 0.3
        arrowImageView.layer.shadowRadius = 2

        // Enable user interaction
        isUserInteractionEnabled = false
    }

    private func createArrowImage() -> UIImage {
        let size = CGSize(width: 50, height: 50)
        let renderer = UIGraphicsImageRenderer(size: size)

        return renderer.image { context in
            let path = UIBezierPath()
            path.move(to: CGPoint(x: size.width / 2, y: 2))
            path.addLine(to: CGPoint(x: size.width - 2, y: size.height - 2))
            path.addLine(to: CGPoint(x: size.width / 2, y: size.height - 8))
            path.addLine(to: CGPoint(x: 2, y: size.height - 2))
            path.close()

            UIColor.systemBlue.setFill()
            path.fill()

            UIColor.white.setStroke()
            path.lineWidth = 1.0
            path.stroke()
        }
    }

    override func prepareForReuse() {
        super.prepareForReuse()
        arrowImageView.transform = .identity
    }

    func updateHeading(_ heading: CLLocationDirection) {
        let radians = heading * .pi / 180.0
        UIView.animate(withDuration: 0.3, delay: 0, options: [.curveEaseInOut, .allowUserInteraction]) {
            self.arrowImageView.transform = CGAffineTransform(rotationAngle: radians)
        }
    }
}
