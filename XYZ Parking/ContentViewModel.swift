//
//  ContentViewModel.swift
//  XYZ Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import Foundation
import SwiftUI
import Combine
import CoreLocation

class ContentViewModel: ObservableObject {
    // MARK: - Services
    @ObservedObject var locationManager = LocationManager()
    @ObservedObject var speechRecognizer = SpeechRecognizer()
    @ObservedObject var dataService = DataService.shared
    
    // MARK: - Outputs
    @Published var filteredParkingLocations: [ParkingLocation] = []
    
    // MARK: - Inputs & UI State
    @Published var searchText = ""
    
    // Private state for data loading logic
    private var hasRequestedInitialFetch = false
    private var hasScheduledLocationFallback = false
    
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        setupPipelines()
    }
    
    private func setupPipelines() {
        // Pipeline to handle filtering and sorting of parking locations
        Publishers.CombineLatest3($searchText, dataService.$parkingLocations, locationManager.$location)
            .debounce(for: .milliseconds(250), scheduler: RunLoop.main) // Debounce to reduce load while typing
            .map { (searchText, allLocations, userLocation) -> [ParkingLocation] in
                
                let filtered: [ParkingLocation]
                
                if searchText.isEmpty {
                    filtered = allLocations
                } else {
                    let lowercasedSearchText = searchText.lowercased()
                    filtered = allLocations.filter { location in
                        location.name.lowercased().contains(lowercasedSearchText) ||
                        location.address.lowercased().contains(lowercasedSearchText) ||
                        (location.onStreet?.lowercased().contains(lowercasedSearchText) ?? false)
                    }
                }
                
                // Sort by distance if user location is available
                if let userLocation = userLocation {
                    return filtered.sorted { loc1, loc2 in
                        let dist1 = CLLocation(latitude: loc1.latitude, longitude: loc1.longitude).distance(from: userLocation)
                        let dist2 = CLLocation(latitude: loc2.latitude, longitude: loc2.longitude).distance(from: userLocation)
                        return dist1 < dist2
                    }
                } else {
                    return Array(filtered.prefix(10)) // Fallback to showing top 10 if no location
                }
            }
            .receive(on: RunLoop.main)
            .assign(to: \.filteredParkingLocations, on: self)
            .store(in: &cancellables)
        
        // Pipeline to sync speech recognizer text with search text
        speechRecognizer.$recognizedText
            .dropFirst()
            .receive(on: RunLoop.main)
            .assign(to: \.searchText, on: self)
            .store(in: &cancellables)
        
        // Pipeline to automatically trigger data loading when location is first acquired
        locationManager.$location
            .dropFirst()
            .sink { [weak self] _ in
                self?.loadParkingDataIfNeeded()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Public Methods (Intents from View)
    
    func onAppear() {
        speechRecognizer.requestPermission()
        loadParkingDataIfNeeded() // Attempt initial load
        print("🚀 ContentViewModel appeared, loading parking data...")
    }
    
    func onRetry() {
        // Reset flags to allow a new fetch attempt
        hasRequestedInitialFetch = false
        hasScheduledLocationFallback = false
        loadParkingDataIfNeeded()
    }
    
    // MARK: - Data Loading Logic (Moved from ContentView)
    
    private func loadParkingDataIfNeeded() {
        guard !hasRequestedInitialFetch else { return }
        
        if let userLocation = locationManager.location {
            print("🚀 Loading parking data around user location via ViewModel")
            dataService.fetchParkingLocations(
                latitude: userLocation.coordinate.latitude,
                longitude: userLocation.coordinate.longitude,
                radius: 0.02
            )
            hasRequestedInitialFetch = true
        } else {
            // Schedule a fallback if location isn't available shortly
            if !hasScheduledLocationFallback {
                hasScheduledLocationFallback = true
                DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { [weak self] in
                    guard let self = self, !self.hasRequestedInitialFetch else { return }
                    
                    let center = self.locationManager.region.center // Melbourne default center
                    print("🧭 Loading parking data around default center (fallback) via ViewModel")
                    self.dataService.fetchParkingLocations(
                        latitude: center.latitude,
                        longitude: center.longitude,
                        radius: 0.02
                    )
                    self.hasRequestedInitialFetch = true
                }
            }
        }
    }
}
